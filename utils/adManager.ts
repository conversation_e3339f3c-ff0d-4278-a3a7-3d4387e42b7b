import mobileAds, { BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
import { Platform } from 'react-native';

// Ad unit IDs for production
const AD_UNIT_IDS = {
  ios: {
    banner: 'ca-app-pub-2947221843793912/1234567890', // Replace with actual iOS banner ad unit ID
  },
  android: {
    banner: 'ca-app-pub-2947221843793912/1234567890', // Replace with actual Android banner ad unit ID
  },
};

// Use test ads in development
const getBannerAdUnitId = (): string => {
  if (__DEV__) {
    return TestIds.BANNER;
  }
  
  return Platform.OS === 'ios' 
    ? AD_UNIT_IDS.ios.banner 
    : AD_UNIT_IDS.android.banner;
};

/**
 * Initialize Google Mobile Ads
 * @returns Promise that resolves when ads are initialized
 */
export const initializeAds = async (): Promise<void> => {
  try {
    await mobileAds().initialize();
    console.log('Google Mobile Ads initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Google Mobile Ads:', error);
    throw error;
  }
};

/**
 * Get the banner ad unit ID for the current platform
 * @returns Banner ad unit ID
 */
export const getBannerAdId = (): string => {
  return getBannerAdUnitId();
};

/**
 * Get the banner ad size
 * @returns Banner ad size configuration
 */
export const getBannerAdSize = () => {
  return BannerAdSize.BANNER; // 320x50
};

/**
 * Check if ads are available (not in a restricted region, etc.)
 * @returns Promise that resolves to boolean indicating if ads are available
 */
export const areAdsAvailable = async (): Promise<boolean> => {
  try {
    // You can add additional checks here if needed
    // For now, we'll assume ads are always available unless in development without test ads
    return true;
  } catch (error) {
    console.error('Error checking ad availability:', error);
    return false;
  }
};
